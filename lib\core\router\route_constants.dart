/// 路由路径常量
///
/// 定义应用中所有路由的路径
class AppRoutes {
  // 主要路由
  static const String splash = '/splash';
  static const String login = '/login';
  static const String register = '/register';
  static const String resetPassword = '/reset_password';
  static const String identityVerification = '/identity_verification';
  static const String home = '/';
  static const String profile = '/profile';
  static const String data = '/data';

  // 实习相关路由
  static const String teacherInternshipList = '/teacher_internship';
  static const String internshipDetail = '/internship/:id';
  static const String internshipApproval = '/internship/:id/approval';
  static const String teacherInternshipPlan = '/teacher_internship_plan';
  static const String teacherInternshipStudent = '/teacher_internship_student';
  static const String teacherFreeInternshipList = '/teacher_internship_exemption';
  static const String teacherSignExemption = '/teacher_sign_exemption';
  static const String teacherInternshipPlanDetail = '/teacher_internship_plan_detail/:planId';
  static const String teacherInternshipGrade = '/teacher_internship_grade';
  static const String teacherFileApproval = '/teacher_file_approval';
  static const String fileApprovalDetail = '/file_approval_detail/:fileId';
  // 文件审核PDF阅读器
  static const String fileApprovalPreviewPdf = '/file_approval_preview_pdf';

  // 学生端
  // 实习投诉
  static const String studentInternshipComplaint = '/student_internship_complaint';
  // 点评老师
  static const String studentEvaluationTeacher = '/student_evaluation_teacher';
  // 实习申请
  static const String studentInternshipApplication = '/student_internship_application';
  // 免实习记录
  static const String studentExemptionRecords = '/student_exemption_records';
  // 就业上报
  static const String studentEmploymentReporting = '/student_employment_reporting';
  // 安全教育（学生端）
  static const String studentSafetyEducation = '/student_safety_education';
  // 实习计划（学生端）
  static const String studentInternshipPlan = '/student_internship_plan';
  // 实习保险信息
  static const String studentInternshipInsuranceInfo = '/internship_insurance_info';
  // 签到
  static const String studentSignIn = '/student_sign_in';
  // 文件上传
  static const String studentFileUpload = '/student_file_upload';
  // 文件上传详情
  static const String studentFileUploadDetail = '/student_file_upload_detail/:fileId';
  // 企业评价
  static const String studentEnterpriseEvaluation = '/student_enterprise_evaluation';
  // 我的成绩
  static  const String studentMyGrade = '/student_my_grade';
  // 签到日历
  static const String studentSignCalendar = '/student_sign_calendar';
  // 我的实习
  static const String studentMyInternship = '/student_my_internship';
  // 日报、周报、月报、总结
  static const String studentDailyReport = '/student_daily_report';
  static const String studentWeeklyReport = '/student_weekly_report';
  static const String studentMonthlyReport = '/student_monthly_report';
  static const String studentSummaryReport = '/student_summary_report';

  // 写报告页面路由
  static const String reportWriteDaily = '/student_report_write_daily';
  static const String reportWriteWeekly = '/student_report_write_weekly';
  static const String reportWriteMonthly = '/student_report_write_monthly';
  static const String reportWriteSummary = '/student_report_write_summary';

  // 请假
  static const String teacherLeave = '/teacher_leave';
  static const String teacherInternshipInfoChange = '/teacher_internship_info_change';
  static const String teacherToEmployment = '/teacher_to_employment';

  // 报告相关路由
  static const String teacherReportDaily = '/teacher_reports_daily';
  static const String teacherReportWeekly = '/teacher_reports_weekly';
  static const String teacherReportMonthly = '/teacher_reports_monthly';
  static const String teacherReportSummary = '/teacher_reports_summary';
  static const String reportDetail = '/report/:id';
  static const String reportApproval = '/report/:id/approval';
  static const String reportSubmit = '/report/submit';

  // 安全教育相关路由
  static const String safetyList = '/safety';
  static const String safetyDetail = '/safety/:id';
  static const String safetyExamNotice = '/safety_exam_notice';
  static const String safetyExam = '/safety_exam';
  static const String teacherSafetyEdu = '/teacher_safety_education';
  static const String teacherSafetyWarning = '/teacher_safety_warning';

  // 设置相关路由
  static const String settings = '/settings';
  static const String changePassword = '/settings/password';

  // 通知
  static const String notice = '/notice';
}

/// 路由参数键名
///
/// 定义路由参数的键名
class RouteParams {
  static const String id = 'id';
  static const String type = 'type';
  static const String status = 'status';
  static const String fileId = 'fileId';
}
