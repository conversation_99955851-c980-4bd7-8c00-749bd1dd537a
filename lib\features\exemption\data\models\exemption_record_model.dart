/// -----
/// exemption_record_model.dart
///
/// 免实习记录数据模型
/// 实现ApprovalItemData接口，用于在审批列表组件中显示
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/widgets/approval_list_item.dart';

/// 免实习记录状态枚举
enum ExemptionStatus {
  /// 待审核
  pending,
  /// 已通过
  approved,
  /// 已拒绝
  rejected,
}

/// 免实习记录数据模型
class ExemptionRecordModel implements ApprovalItemData {
  /// 记录ID
  final String recordId;
  
  /// 学生姓名
  final String studentName;
  
  /// 学生头像URL
  final String studentAvatar;
  
  /// 申请状态
  final ExemptionStatus exemptionStatus;
  
  /// 提交时间
  final DateTime submitDateTime;
  
  /// 审核时间（可选）
  final DateTime? reviewTime;
  
  /// 审核意见（可选）
  final String? reviewComment;
  
  /// 免实习原因
  final String reason;
  
  /// 证明文件URL（可选）
  final String? proofDocumentUrl;
  
  /// 证明文件名称（可选）
  final String? proofDocumentName;
  
  /// 实习计划ID
  final String planId;
  
  /// 实习计划名称
  final String planName;

  const ExemptionRecordModel({
    required this.recordId,
    required this.studentName,
    required this.studentAvatar,
    required this.exemptionStatus,
    required this.submitDateTime,
    this.reviewTime,
    this.reviewComment,
    required this.reason,
    this.proofDocumentUrl,
    this.proofDocumentName,
    required this.planId,
    required this.planName,
  });

  /// 实现ApprovalItemData接口的id字段
  @override
  String get id => recordId;

  /// 实现ApprovalItemData接口的status字段
  @override
  String get status {
    switch (exemptionStatus) {
      case ExemptionStatus.pending:
        return '待审核';
      case ExemptionStatus.approved:
        return '已通过';
      case ExemptionStatus.rejected:
        return '已拒绝';
    }
  }

  /// 实现ApprovalItemData接口的submitTime字段
  @override
  String get submitTime {
    return '${submitDateTime.year}-${submitDateTime.month.toString().padLeft(2, '0')}-${submitDateTime.day.toString().padLeft(2, '0')} ${submitDateTime.hour.toString().padLeft(2, '0')}:${submitDateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 从JSON创建模型实例
  factory ExemptionRecordModel.fromJson(Map<String, dynamic> json) {
    return ExemptionRecordModel(
      recordId: json['recordId'] ?? '',
      studentName: json['studentName'] ?? '',
      studentAvatar: json['studentAvatar'] ?? '',
      exemptionStatus: _parseStatus(json['status']),
      submitDateTime: DateTime.parse(json['submitTime']),
      reviewTime: json['reviewTime'] != null ? DateTime.parse(json['reviewTime']) : null,
      reviewComment: json['reviewComment'],
      reason: json['reason'] ?? '',
      proofDocumentUrl: json['proofDocumentUrl'],
      proofDocumentName: json['proofDocumentName'],
      planId: json['planId'] ?? '',
      planName: json['planName'] ?? '',
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'recordId': recordId,
      'studentName': studentName,
      'studentAvatar': studentAvatar,
      'status': _statusToString(exemptionStatus),
      'submitTime': submitDateTime.toIso8601String(),
      'reviewTime': reviewTime?.toIso8601String(),
      'reviewComment': reviewComment,
      'reason': reason,
      'proofDocumentUrl': proofDocumentUrl,
      'proofDocumentName': proofDocumentName,
      'planId': planId,
      'planName': planName,
    };
  }

  /// 解析状态字符串为枚举
  static ExemptionStatus _parseStatus(dynamic status) {
    if (status is int) {
      switch (status) {
        case 0:
          return ExemptionStatus.pending;
        case 1:
          return ExemptionStatus.approved;
        case 2:
          return ExemptionStatus.rejected;
        default:
          return ExemptionStatus.pending;
      }
    } else if (status is String) {
      switch (status.toLowerCase()) {
        case 'pending':
        case '待审核':
          return ExemptionStatus.pending;
        case 'approved':
        case '已通过':
          return ExemptionStatus.approved;
        case 'rejected':
        case '已拒绝':
          return ExemptionStatus.rejected;
        default:
          return ExemptionStatus.pending;
      }
    }
    return ExemptionStatus.pending;
  }

  /// 将状态枚举转换为字符串
  static String _statusToString(ExemptionStatus status) {
    switch (status) {
      case ExemptionStatus.pending:
        return 'pending';
      case ExemptionStatus.approved:
        return 'approved';
      case ExemptionStatus.rejected:
        return 'rejected';
    }
  }

  /// 创建副本
  ExemptionRecordModel copyWith({
    String? recordId,
    String? studentName,
    String? studentAvatar,
    ExemptionStatus? exemptionStatus,
    DateTime? submitDateTime,
    DateTime? reviewTime,
    String? reviewComment,
    String? reason,
    String? proofDocumentUrl,
    String? proofDocumentName,
    String? planId,
    String? planName,
  }) {
    return ExemptionRecordModel(
      recordId: recordId ?? this.recordId,
      studentName: studentName ?? this.studentName,
      studentAvatar: studentAvatar ?? this.studentAvatar,
      exemptionStatus: exemptionStatus ?? this.exemptionStatus,
      submitDateTime: submitDateTime ?? this.submitDateTime,
      reviewTime: reviewTime ?? this.reviewTime,
      reviewComment: reviewComment ?? this.reviewComment,
      reason: reason ?? this.reason,
      proofDocumentUrl: proofDocumentUrl ?? this.proofDocumentUrl,
      proofDocumentName: proofDocumentName ?? this.proofDocumentName,
      planId: planId ?? this.planId,
      planName: planName ?? this.planName,
    );
  }

  /// 创建示例数据（用于测试和开发）
  static List<ExemptionRecordModel> getSampleData() {
    return [
      ExemptionRecordModel(
        recordId: '1',
        studentName: '张三',
        studentAvatar: '',
        exemptionStatus: ExemptionStatus.pending,
        submitDateTime: DateTime.now().subtract(const Duration(days: 2)),
        reason: '参军入伍',
        planId: 'plan_001',
        planName: '2021级市场销售2023-2024实习学年第二学期岗位实习',
      ),
      ExemptionRecordModel(
        recordId: '2',
        studentName: '李四',
        studentAvatar: '',
        exemptionStatus: ExemptionStatus.approved,
        submitDateTime: DateTime.now().subtract(const Duration(days: 5)),
        reviewTime: DateTime.now().subtract(const Duration(days: 1)),
        reviewComment: '材料齐全，同意免实习申请',
        reason: '升学深造',
        proofDocumentName: '录取通知书.pdf',
        planId: 'plan_001',
        planName: '2021级市场销售2023-2024实习学年第二学期岗位实习',
      ),
      ExemptionRecordModel(
        recordId: '3',
        studentName: '王五',
        studentAvatar: '',
        exemptionStatus: ExemptionStatus.rejected,
        submitDateTime: DateTime.now().subtract(const Duration(days: 7)),
        reviewTime: DateTime.now().subtract(const Duration(days: 3)),
        reviewComment: '证明材料不足，请补充相关证明',
        reason: '创业',
        planId: 'plan_001',
        planName: '2021级市场销售2023-2024实习学年第二学期岗位实习',
      ),
    ];
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ExemptionRecordModel && other.recordId == recordId;
  }

  @override
  int get hashCode => recordId.hashCode;

  @override
  String toString() {
    return 'ExemptionRecordModel(recordId: $recordId, studentName: $studentName, status: $status, submitTime: $submitTime)';
  }
}
