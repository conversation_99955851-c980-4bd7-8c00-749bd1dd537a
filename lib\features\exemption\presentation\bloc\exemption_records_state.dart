/// -----
/// exemption_records_state.dart
///
/// 免实习记录BLoC状态定义
/// 定义所有与免实习记录相关的状态
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import '../../data/models/exemption_record_model.dart';

/// 免实习记录状态基类
///
/// 所有免实习记录相关的状态都应继承此类
abstract class ExemptionRecordsState extends Equatable {
  const ExemptionRecordsState();

  @override
  List<Object?> get props => [];
}

/// 免实习记录初始状态
///
/// 表示免实习记录功能的初始状态
class ExemptionRecordsInitialState extends ExemptionRecordsState {
  const ExemptionRecordsInitialState();
}

/// 免实习记录加载中状态
///
/// 表示正在从服务器加载免实习记录数据
class ExemptionRecordsLoadingState extends ExemptionRecordsState {
  const ExemptionRecordsLoadingState();
}

/// 免实习记录加载成功状态
///
/// 表示成功加载了免实习记录列表数据
class ExemptionRecordsLoadedState extends ExemptionRecordsState {
  /// 免实习记录列表
  final List<ExemptionRecordModel> records;
  
  /// 当前实习计划ID
  final String? currentPlanId;
  
  /// 当前筛选状态
  final String? statusFilter;
  
  /// 搜索关键词
  final String? searchKeyword;
  
  /// 是否还有更多数据
  final bool hasMore;
  
  /// 当前页码
  final int currentPage;
  
  /// 总记录数
  final int totalCount;

  const ExemptionRecordsLoadedState({
    required this.records,
    this.currentPlanId,
    this.statusFilter,
    this.searchKeyword,
    this.hasMore = false,
    this.currentPage = 1,
    this.totalCount = 0,
  });

  @override
  List<Object?> get props => [
    records,
    currentPlanId,
    statusFilter,
    searchKeyword,
    hasMore,
    currentPage,
    totalCount,
  ];

  /// 创建副本
  ExemptionRecordsLoadedState copyWith({
    List<ExemptionRecordModel>? records,
    String? currentPlanId,
    String? statusFilter,
    String? searchKeyword,
    bool? hasMore,
    int? currentPage,
    int? totalCount,
  }) {
    return ExemptionRecordsLoadedState(
      records: records ?? this.records,
      currentPlanId: currentPlanId ?? this.currentPlanId,
      statusFilter: statusFilter ?? this.statusFilter,
      searchKeyword: searchKeyword ?? this.searchKeyword,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
      totalCount: totalCount ?? this.totalCount,
    );
  }

  /// 获取待审核记录数量
  int get pendingCount => records.where((record) => record.exemptionStatus == ExemptionStatus.pending).length;

  /// 获取已通过记录数量
  int get approvedCount => records.where((record) => record.exemptionStatus == ExemptionStatus.approved).length;

  /// 获取已拒绝记录数量
  int get rejectedCount => records.where((record) => record.exemptionStatus == ExemptionStatus.rejected).length;
}

/// 免实习记录刷新中状态
///
/// 表示正在刷新免实习记录数据，保留之前的数据
class ExemptionRecordsRefreshingState extends ExemptionRecordsState {
  /// 之前的记录列表
  final List<ExemptionRecordModel> previousRecords;
  
  /// 当前实习计划ID
  final String? currentPlanId;

  const ExemptionRecordsRefreshingState({
    required this.previousRecords,
    this.currentPlanId,
  });

  @override
  List<Object?> get props => [previousRecords, currentPlanId];
}

/// 免实习记录加载更多中状态
///
/// 表示正在加载更多免实习记录数据
class ExemptionRecordsLoadingMoreState extends ExemptionRecordsState {
  /// 当前已有的记录列表
  final List<ExemptionRecordModel> currentRecords;
  
  /// 当前实习计划ID
  final String? currentPlanId;

  const ExemptionRecordsLoadingMoreState({
    required this.currentRecords,
    this.currentPlanId,
  });

  @override
  List<Object?> get props => [currentRecords, currentPlanId];
}

/// 免实习记录错误状态
///
/// 表示加载免实习记录时发生错误
class ExemptionRecordsErrorState extends ExemptionRecordsState {
  /// 错误消息
  final String message;
  
  /// 之前的记录列表（如果有的话）
  final List<ExemptionRecordModel>? previousRecords;

  const ExemptionRecordsErrorState({
    required this.message,
    this.previousRecords,
  });

  @override
  List<Object?> get props => [message, previousRecords];
}

/// 免实习记录操作中状态
///
/// 表示正在执行某个操作（如删除、重新提交等）
class ExemptionRecordsOperatingState extends ExemptionRecordsState {
  /// 操作类型
  final String operationType;
  
  /// 操作的记录ID
  final String recordId;
  
  /// 当前记录列表
  final List<ExemptionRecordModel> currentRecords;

  const ExemptionRecordsOperatingState({
    required this.operationType,
    required this.recordId,
    required this.currentRecords,
  });

  @override
  List<Object?> get props => [operationType, recordId, currentRecords];
}

/// 免实习记录操作成功状态
///
/// 表示操作成功完成
class ExemptionRecordsOperationSuccessState extends ExemptionRecordsState {
  /// 操作类型
  final String operationType;
  
  /// 成功消息
  final String message;
  
  /// 更新后的记录列表
  final List<ExemptionRecordModel> updatedRecords;

  const ExemptionRecordsOperationSuccessState({
    required this.operationType,
    required this.message,
    required this.updatedRecords,
  });

  @override
  List<Object?> get props => [operationType, message, updatedRecords];
}

/// 免实习记录空状态
///
/// 表示没有找到任何免实习记录
class ExemptionRecordsEmptyState extends ExemptionRecordsState {
  /// 当前实习计划ID
  final String? currentPlanId;
  
  /// 空状态消息
  final String message;

  const ExemptionRecordsEmptyState({
    this.currentPlanId,
    this.message = '暂无免实习记录',
  });

  @override
  List<Object?> get props => [currentPlanId, message];
}
