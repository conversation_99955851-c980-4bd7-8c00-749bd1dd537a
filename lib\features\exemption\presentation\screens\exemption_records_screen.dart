/// -----
/// exemption_records_screen.dart
///
/// 学生端免实习记录页面
/// 显示当前学生的免实习申请记录，支持查看申请状态和添加新申请
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/approval_list_item.dart';
import 'package:flutter_demo/core/widgets/page_state_widget.dart';
import 'package:flutter_demo/features/plan/presentation/screens/student_exemption_application_screen.dart';
import '../bloc/exemption_records_bloc.dart';
import '../bloc/exemption_records_event.dart';
import '../bloc/exemption_records_state.dart';
import '../../data/models/exemption_record_model.dart';

/// 学生端免实习记录页面
///
/// 功能包括：
/// - 显示当前学生的免实习申请记录
/// - 支持查看申请状态（待审核、已通过、已拒绝）
/// - 支持添加新的免实习申请
/// - 支持下拉刷新和上拉加载更多
class ExemptionRecordsScreen extends StatelessWidget {
  const ExemptionRecordsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<ExemptionRecordsBloc>()
        ..add(const LoadExemptionRecordsEvent()),
      child: const _ExemptionRecordsView(),
    );
  }
}

class _ExemptionRecordsView extends StatelessWidget {
  const _ExemptionRecordsView();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: const CustomAppBar(
        title: '免实习',
        backgroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // 课程信息头部
          const CourseHeaderSection(),
          
          // 记录列表区域
          Expanded(
            child: BlocBuilder<ExemptionRecordsBloc, ExemptionRecordsState>(
              builder: (context, state) {
                return _buildContent(context, state);
              },
            ),
          ),
        ],
      ),
      // 右下角浮动按钮
      floatingActionButton: _buildFloatingActionButton(context),
    );
  }

  /// 构建主要内容区域
  Widget _buildContent(BuildContext context, ExemptionRecordsState state) {
    if (state is ExemptionRecordsLoadingState) {
      return const PageStateWidget(
        state: PageStateType.initialLoading,
        loadingMessage: '加载免实习记录中...',
      );
    }

    if (state is ExemptionRecordsErrorState) {
      return PageStateWidget(
        state: PageStateType.serverError,
        errorMessage: state.message,
        onRetry: () {
          context.read<ExemptionRecordsBloc>().add(const LoadExemptionRecordsEvent());
        },
      );
    }

    if (state is ExemptionRecordsEmptyState) {
      return PageStateWidget(
        state: PageStateType.empty,
        emptyMessage: state.message,
      );
    }

    if (state is ExemptionRecordsLoadedState) {
      return _buildRecordsList(context, state);
    }

    if (state is ExemptionRecordsRefreshingState) {
      return _buildRecordsListWithRefreshing(context, state.previousRecords);
    }

    return const PageStateWidget(
      state: PageStateType.empty,
    );
  }

  /// 构建记录列表
  Widget _buildRecordsList(BuildContext context, ExemptionRecordsLoadedState state) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<ExemptionRecordsBloc>().add(const RefreshExemptionRecordsEvent());
      },
      child: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.h),
        itemCount: state.records.length,
        itemBuilder: (context, index) {
          final record = state.records[index];
          return Container(
            margin: EdgeInsets.only(bottom: 20.h),
            child: ApprovalListItem(
              item: record,
              isPending: record.exemptionStatus == ExemptionStatus.pending,
              onTap: () => _onRecordTap(context, record),
              contentBuilder: (item) => _buildRecordContent(record),
              attachmentBuilder: (item) => _buildRecordAttachment(record),
            ),
          );
        },
      ),
    );
  }

  /// 构建带刷新状态的记录列表
  Widget _buildRecordsListWithRefreshing(BuildContext context, List<ExemptionRecordModel> records) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<ExemptionRecordsBloc>().add(const RefreshExemptionRecordsEvent());
      },
      child: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.h),
        itemCount: records.length,
        itemBuilder: (context, index) {
          final record = records[index];
          return Container(
            margin: EdgeInsets.only(bottom: 20.h),
            child: ApprovalListItem(
              item: record,
              isPending: record.exemptionStatus == ExemptionStatus.pending,
              onTap: () => _onRecordTap(context, record),
              contentBuilder: (item) => _buildRecordContent(record),
              attachmentBuilder: (item) => _buildRecordAttachment(record),
            ),
          );
        },
      ),
    );
  }

  /// 构建记录内容区域
  Widget _buildRecordContent(ExemptionRecordModel record) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 免实习原因
        Text(
          '免实习原因：${record.reason}',
          style: TextStyle(
            fontSize: 14.sp,
            color: AppTheme.textSecondaryColor,
          ),
        ),
        SizedBox(height: 8.h),
        
        // 实习计划名称
        Text(
          record.planName,
          style: TextStyle(
            fontSize: 12.sp,
            color: AppTheme.textHintColor,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        
        // 审核意见（如果有）
        if (record.reviewComment != null && record.reviewComment!.isNotEmpty) ...[
          SizedBox(height: 8.h),
          Text(
            '审核意见：${record.reviewComment}',
            style: TextStyle(
              fontSize: 12.sp,
              color: record.exemptionStatus == ExemptionStatus.approved 
                  ? Colors.green 
                  : Colors.red,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  /// 构建记录附件区域
  Widget _buildRecordAttachment(ExemptionRecordModel record) {
    if (record.proofDocumentName == null || record.proofDocumentName!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.only(top: 12.h),
      child: Row(
        children: [
          Icon(
            Icons.attach_file,
            size: 16.sp,
            color: AppTheme.textSecondaryColor,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              record.proofDocumentName!,
              style: TextStyle(
                fontSize: 12.sp,
                color: AppTheme.primaryColor,
                decoration: TextDecoration.underline,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建右下角浮动按钮
  Widget _buildFloatingActionButton(BuildContext context) {
    return Container(
      width: 80.w,
      height: 80.w,
      margin: EdgeInsets.only(right: 20.w, bottom: 20.h),
      child: FloatingActionButton(
        onPressed: () => _onAddNewApplication(context),
        backgroundColor: const Color(0xFF2165F6),
        elevation: 4,
        child: Icon(
          Icons.add,
          color: Colors.white,
          size: 32.sp,
        ),
      ),
    );
  }

  /// 处理记录点击事件
  void _onRecordTap(BuildContext context, ExemptionRecordModel record) {
    // TODO(feature): 导航到免实习记录详情页面
    // 这里可以显示详情弹窗或导航到详情页面
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('记录详情'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('申请状态：${record.status}'),
            SizedBox(height: 8.h),
            Text('免实习原因：${record.reason}'),
            SizedBox(height: 8.h),
            Text('提交时间：${record.submitTime}'),
            if (record.reviewTime != null) ...[
              SizedBox(height: 8.h),
              Text('审核时间：${record.reviewTime!.year}-${record.reviewTime!.month.toString().padLeft(2, '0')}-${record.reviewTime!.day.toString().padLeft(2, '0')}'),
            ],
            if (record.reviewComment != null && record.reviewComment!.isNotEmpty) ...[
              SizedBox(height: 8.h),
              Text('审核意见：${record.reviewComment}'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 处理添加新申请事件
  void _onAddNewApplication(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const StudentExemptionApplicationScreen(),
      ),
    ).then((_) {
      // 返回时刷新列表
      if (context.mounted) {
        context.read<ExemptionRecordsBloc>().add(const RefreshExemptionRecordsEvent());
      }
    });
  }
}
